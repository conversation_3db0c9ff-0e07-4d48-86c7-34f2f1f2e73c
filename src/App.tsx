
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import OAuthCallback from "./pages/OAuthCallback";
import Onboarding1 from "./pages/Onboarding1";
import Onboarding2 from "./pages/Onboarding2";
import Onboarding3 from "./pages/Onboarding3";
import Dashboard from "./pages/Dashboard";
import Project from "./pages/Channels/Chatbot/components/Project.tsx";
import KnowledgeBase from "./pages/KnowledgeBase";
import FormBuilder from "./pages/FormBuilder";
import FormPreview from "./pages/FormBuilder/FormPreview";
import PublicForm from "./pages/FormBuilder/PublicForm";
import BillingPlan from "./pages/BillingPlan";
import ChatRecords from "./pages/ChatRecords";
import ChatbotManagement from "./pages/Channels/Chatbot";
import TestInstall from "./pages/Channels/Chatbot/components/TestInstall.tsx";
import Documentation from "./pages/Documentation";
import Account from "./pages/Account";
import NotFound from "./pages/NotFound";
import { AuthProvider } from "./context/AuthContext";
import { ProjectProvider } from "./context/ProjectContext";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <ProjectProvider>
            <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/auth/:provider/callback" element={<OAuthCallback />} />
            <Route path="/onboarding-1" element={<Onboarding1 />} />
            <Route path="/onboarding-2" element={<Onboarding2 />} />
            <Route path="/onboarding-3" element={<Onboarding3 />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/project" element={<Project />} />
            <Route path="/project/:id" element={<Project />} />
            <Route path="/knowledge" element={<KnowledgeBase />} />
            <Route path="/form-builder" element={<FormBuilder />} />
            <Route path="/form-preview/:formId" element={<FormPreview />} />
            <Route path="/form/:formId" element={<PublicForm />} />
            <Route path="/chats" element={<ChatbotManagement />} />
            <Route path="/chat-records" element={<ChatRecords />} />
            <Route path="/test-install" element={<TestInstall />} />
            <Route path="/documentation" element={<Documentation />} />
            <Route path="/documentation/:docId" element={<Documentation />} />
            <Route path="/account" element={<Account />} />
            <Route path="/billing" element={<BillingPlan />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
          </ProjectProvider>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
