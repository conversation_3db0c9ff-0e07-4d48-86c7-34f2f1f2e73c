// 用户信息获取工具函数

export interface UserSystemInfo {
  country: string;
  city: string;
  browser: string;
  system: string;
}

/**
 * 获取浏览器信息
 */
export function getBrowserInfo(): string {
  const userAgent = navigator.userAgent;
  
  if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
    return 'Chrome';
  } else if (userAgent.includes('Firefox')) {
    return 'Firefox';
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    return 'Safari';
  } else if (userAgent.includes('Edg')) {
    return 'Edge';
  } else if (userAgent.includes('Opera') || userAgent.includes('OPR')) {
    return 'Opera';
  } else {
    return 'Unknown';
  }
}

/**
 * 获取操作系统信息
 */
export function getSystemInfo(): string {
  const userAgent = navigator.userAgent;
  const platform = navigator.platform;
  
  if (userAgent.includes('Windows') || platform.includes('Win')) {
    return 'Windows';
  } else if (userAgent.includes('Mac') || platform.includes('Mac')) {
    return 'macOS';
  } else if (userAgent.includes('Linux') || platform.includes('Linux')) {
    return 'Linux';
  } else if (userAgent.includes('Android')) {
    return 'Android';
  } else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
    return 'iOS';
  } else {
    return 'Unknown';
  }
}

/**
 * 获取用户语言
 */
export function getUserLanguage(): string {
  return navigator.language || navigator.languages?.[0] || 'en-US';
}

/**
 * 获取屏幕分辨率
 */
export function getScreenResolution(): string {
  return `${screen.width}x${screen.height}`;
}

/**
 * 通过IP获取地理位置信息（使用免费的IP地理位置API）
 */
export async function getLocationInfo(): Promise<{ country: string; city: string }> {
  try {
    // 使用免费的IP地理位置API
    const response = await fetch('https://ipapi.co/json/');
    
    if (response.ok) {
      const data = await response.json();
      return {
        country: data.country_name || 'Unknown',
        city: data.city || 'Unknown',
      };
    }
  } catch (error) {
    console.warn('Failed to get location info:', error);
  }
  
  // 尝试从语言推断国家
  const language = getUserLanguage();
  const countryFromLang = getCountryFromLanguage(language);
  
  return {
    country: countryFromLang,
    city: 'Unknown',
  };
}

/**
 * 从语言代码推断国家
 */
function getCountryFromLanguage(language: string): string {
  const langCountryMap: Record<string, string> = {
    'zh-CN': 'China',
    'zh-TW': 'Taiwan',
    'zh-HK': 'Hong Kong',
    'en-US': 'United States',
    'en-GB': 'United Kingdom',
    'ja-JP': 'Japan',
    'ko-KR': 'South Korea',
    'fr-FR': 'France',
    'de-DE': 'Germany',
    'es-ES': 'Spain',
    'it-IT': 'Italy',
    'ru-RU': 'Russia',
    'pt-BR': 'Brazil',
    'ar-SA': 'Saudi Arabia',
  };
  
  return langCountryMap[language] || langCountryMap[language.split('-')[0]] || 'Unknown';
}

/**
 * 获取完整的用户系统信息
 */
export async function getUserSystemInfo(): Promise<UserSystemInfo> {
  const locationInfo = await getLocationInfo();
  
  return {
    country: locationInfo.country,
    city: locationInfo.city,
    browser: getBrowserInfo(),
    system: getSystemInfo(),
  };
}

/**
 * 生成会话存储的key
 */
export function getSessionStorageKey(projectId: string): string {
  return `chat_session_${projectId}`;
}

/**
 * 从localStorage获取会话ID
 */
export function getStoredSessionId(projectId: string): string | null {
  try {
    return localStorage.getItem(getSessionStorageKey(projectId));
  } catch (error) {
    console.warn('Failed to get stored session ID:', error);
    return null;
  }
}

/**
 * 将会话ID存储到localStorage
 */
export function storeSessionId(projectId: string, sessionId: string): void {
  try {
    localStorage.setItem(getSessionStorageKey(projectId), sessionId);
  } catch (error) {
    console.warn('Failed to store session ID:', error);
  }
}

/**
 * 清除存储的会话ID
 */
export function clearStoredSessionId(projectId: string): void {
  try {
    localStorage.removeItem(getSessionStorageKey(projectId));
  } catch (error) {
    console.warn('Failed to clear stored session ID:', error);
  }
}
