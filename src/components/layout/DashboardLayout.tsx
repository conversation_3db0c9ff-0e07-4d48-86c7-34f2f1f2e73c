import React from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  Bot,
  LayoutDashboard,
  MessageSquare,
  LogOut,
  User,
  ChevronDown,
  Settings,
  CreditCard,
  FileText,
  Phone,
  Mail,
  Mic,
  Workflow,
  Database,
  GitBranch,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const { user, logout, isLoading } = useAuth();
  const { currentProject, projects, setCurrentProject } = useProject();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  // 菜单数据结构
  const menuData = [
    {
      title: t("dashboard"),
      url: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: t("channels"),
      icon: MessageSquare,
      items: [
        {
          title: t("chatbot"),
          url: "/chats",
          icon: Bot,
        },
        {
          title: t("whatsapp"),
          url: "/channels/whatsapp",
          icon: Phone,
        },
        {
          title: t("voice"),
          url: "/channels/voice",
          icon: Mic,
        },
        {
          title: t("mail"),
          url: "/channels/mail",
          icon: Mail,
        },
      ],
    },
    {
      title: t("knowledgeBase"),
      url: "/knowledge",
      icon: Database,
    },
    {
      title: t("workflow"),
      icon: Workflow,
      items: [
        {
          title: t("form"),
          url: "/form-builder",
          icon: FileText,
        },
        {
          title: t("pipeline"),
          url: "/workflow/pipeline",
          icon: GitBranch,
        },
      ],
    },
    {
      title: t("chats"),
      url: "/chat-records",
      icon: MessageSquare,
    },
  ];

  const isActive = (href: string) => location.pathname === href;

  return (
    <SidebarProvider>
      <Sidebar variant="inset">
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <div className="flex items-center">
                <Bot className="h-8 w-8 text-blue-600" />
                <div className="ml-2 flex items-center">
                  <span className="text-xl font-bold text-gray-900">
                    {t("aiAssistant")}
                  </span>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="ml-2 p-1">
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="start"
                      className="w-56 bg-white border shadow-lg z-50"
                    >
                      {projects.map((project) => (
                        <DropdownMenuItem
                          key={project.id}
                          onClick={() => setCurrentProject(project)}
                          className={
                            currentProject?.id === project.id
                              ? "bg-blue-50 text-blue-600"
                              : ""
                          }
                        >
                          {project.name}
                        </DropdownMenuItem>
                      ))}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => navigate("/onboarding-1")}>
                        {t("newProject")}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                {menuData.map((item) => (
                  <Collapsible
                    key={item.title}
                    asChild
                    defaultOpen={item.items?.some(subItem => isActive(subItem.url))}
                    className="group/collapsible"
                  >
                    <SidebarMenuItem>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton
                          tooltip={item.title}
                          isActive={item.url ? isActive(item.url) : false}
                          asChild={!!item.url}
                        >
                          {item.url ? (
                            <Link to={item.url}>
                              <item.icon />
                              <span>{item.title}</span>
                            </Link>
                          ) : (
                            <>
                              <item.icon />
                              <span>{item.title}</span>
                              {item.items && <ChevronDown className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-180" />}
                            </>
                          )}
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                      {item.items && (
                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {item.items.map((subItem) => (
                              <SidebarMenuSubItem key={subItem.title}>
                                <SidebarMenuSubButton
                                  asChild
                                  isActive={isActive(subItem.url)}
                                >
                                  <Link to={subItem.url}>
                                    <subItem.icon />
                                    <span>{subItem.title}</span>
                                  </Link>
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            ))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      )}
                    </SidebarMenuItem>
                  </Collapsible>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          {isLoading ? (
            // 加载状态
            <div className="flex items-center p-2">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center animate-pulse">
                <User className="w-4 h-4 text-gray-500" />
              </div>
              <div className="ml-3">
                <div
                  className="h-4 bg-gray-300 rounded animate-pulse mb-1"
                  style={{ width: "80px" }}
                ></div>
                <div
                  className="h-3 bg-gray-200 rounded animate-pulse"
                  style={{ width: "60px" }}
                ></div>
              </div>
            </div>
          ) : (
            // 用户信息下拉菜单
            <SidebarMenu>
              <SidebarMenuItem>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <SidebarMenuButton
                      size="lg"
                      className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                    >
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {user?.name ? (
                          user.name.charAt(0).toUpperCase()
                        ) : (
                          <User className="w-4 h-4" />
                        )}
                      </div>
                      <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold">
                          {user?.name || "User"}
                        </span>
                      </div>
                      <ChevronDown className="ml-auto size-4" />
                    </SidebarMenuButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                    side="bottom"
                    align="end"
                    sideOffset={4}
                  >
                    <DropdownMenuItem className="flex items-center">
                      <User className="mr-2 h-4 w-4" />
                      <div>
                        <div className="font-medium">{user?.name || "User"}</div>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => navigate("/account")}
                      className="flex items-center"
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      {t("account")}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => navigate("/billing")}
                      className="flex items-center"
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      {t("billing")}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={logout}
                      className="text-red-600 focus:text-red-600"
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      {t("logout")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </SidebarMenuItem>
            </SidebarMenu>
          )}
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
};
