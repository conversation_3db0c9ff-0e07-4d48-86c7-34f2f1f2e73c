import { useToast } from "@/components/ui/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  deleteUserAccount,
  getProjects,
  updateUserProfile,
  userLogin,
} from "@/services/api";
import { responseCode } from "@/services/types";
import { jwtDecode } from "jwt-decode";
import React, { createContext, useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import store from "store2";

interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string | null;
  role: "admin" | "user";
}

interface CustomJwtPayload {
  sub?: string;
  name?: string;
  roles?: string[];
  exp?: number;
  iat?: number;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => Promise<void>;
  deleteAccount: (password: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    // Check if user is already logged in
    const storedUser = store.get("user");
    const storedToken = store.get("token");

    if (storedUser && storedToken) {
      try {
        // 验证 token 是否过期
        const decoded = jwtDecode<CustomJwtPayload>(storedToken);
        const currentTime = Date.now() / 1000;

        if (decoded.exp && decoded.exp > currentTime) {
          // Token 仍然有效
          setUser(JSON.parse(storedUser));
        } else {
          store.remove("user");
          store.remove("token");
        }
      } catch (error) {
        console.error("Failed to parse stored user or token:", error);
        store.remove("user");
        store.remove("token");
      }
    }

    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);

    try {
      const response = await userLogin({ email: email, password });
      if (response.token) {
        store.set("token", response.token);
        const result = jwtDecode<CustomJwtPayload>(response.token);
        if (result) {
          const userData = {
            id: result?.sub || "",
            name: result?.name || "",
            email: email, // Assuming username is email
            avatar: null,
            role: (result?.roles?.[0] as "admin" | "user") || "user",
          };
          setUser(userData);
          store.set("user", JSON.stringify(userData));
          toast({
            title: t("loginSuccessful"),
            description: `${t("loginTitle")}, ${email}!`,
            variant: "default",
            duration: 5000,
          });

          // 检查项目数量决定跳转页面
          try {
            const projects = await getProjects();
            if (projects.length === 0) {
              navigate("/onboarding-1");
            } else {
              navigate("/dashboard");
            }
          } catch (error) {
            console.error("Failed to get projects:", error);
            // 如果获取项目失败，默认跳转到onboarding
            navigate("/onboarding-1");
          }
        } else {
          throw new Error("Token failed");
        }
      } else {
        throw new Error("Authentication failed");
      }
    } catch (error) {
      // store.remove('user')
      toast({
        title: t("loginFailed"),
        description: t("loginFailedDescription"),
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    store.remove("token");
    store.remove("user");
    setUser(null);
    navigate("/login");
    toast({
      title: t("logout"),
      description: t("logoutDescription"),
      variant: "default",
      duration: 5000,
    });
  };
  const updateProfile = async (data: User) => {
    if (!user) return;

    try {
      await updateUserProfile(data);
      setUser(data);
      store.set("user", JSON.stringify(data));

      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
        variant: "default",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: error.message,
        variant: "destructive",
        duration: 3000,
      });
      throw error;
    }
  };

  const deleteAccount = async (password: string) => {
    try {
      // Make API call to delete the account with password verification
      await deleteUserAccount(user.id, password);

      // Log out the user after successful deletion
      logout();

      toast({
        title: "Account Deleted",
        description: "Your account has been deleted successfully.",
        variant: "default",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Failed to delete account. Please try again.",
        variant: "destructive",
        duration: 3000,
      });
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user && !!store.get("token"),
        isLoading,
        login,
        logout,
        updateProfile,
        deleteAccount,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
