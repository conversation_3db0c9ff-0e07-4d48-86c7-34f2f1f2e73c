import { useState, useEffect } from 'react';

interface OnboardingData {
  websiteUrl: string;
  brandName: string;
  brandColor: string;
  logo: string | null;
  welcomeMessage: string;
  suggestedQuestions: string[];
  suggestedQuestionsEnabled: boolean;
}

const STORAGE_KEY = 'onboarding_data';

const defaultData: OnboardingData = {
  websiteUrl: '',
  brandName: 'Hali',
  brandColor: '#FFAB01',
  logo: null,
  welcomeMessage: "Welcome! 👋\n\nI'm Hali, here to assist with any questions you have. How can I help you today?",
  suggestedQuestions: ['No questions.', 'How are you?', 'I have another question'],
  suggestedQuestionsEnabled: true,
};

export const useOnboardingData = () => {
  const [data, setData] = useState<OnboardingData>(() => {
    // 在初始化时就从 localStorage 读取数据
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.error('Failed to parse onboarding data:', error);
      }
    }
    return defaultData;
  });

  const updateData = (updates: Partial<OnboardingData>) => {
    setData(prev => {
      const newData = { ...prev, ...updates };
      // 确保在数据更新后立即存储到 localStorage
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(newData));
        console.log('Data saved to localStorage:', newData);
      } catch (error) {
        console.error('Failed to save to localStorage:', error);
      }
      return newData;
    });
  };

  // 可选：添加一个清除数据的方法
  const clearData = () => {
    localStorage.removeItem(STORAGE_KEY);
    setData(defaultData);
  };

  return {
    data,
    updateData,
    clearData,
  };
};