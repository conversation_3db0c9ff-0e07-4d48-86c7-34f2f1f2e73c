import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { Eye, Loader2, Redo, Save, Undo } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ComponentLibrary } from "./components/ComponentLibrary";
import { DesignCanvas } from "./components/DesignCanvas";
import { LogoUpload } from "./components/LogoUpload";
import { PropertyPanel } from "./components/PropertyPanel";
import { formApi } from "./services/form-api";
import { FormComponent } from "./types/form-types";
import { generateFormId, serializeForm } from "./utils/form-serializer";

interface FormDesignerProps {
  projectId: string;
  backToList: () => void;
}

export const FormDesigner = ({ projectId, backToList }: FormDesignerProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [formId, setFormId] = useState<string>("");
  const [formTitle, setFormTitle] = useState("");
  const [formDescription, setFormDescription] = useState("");
  const [formLogo, setFormLogo] = useState<string | null>(null);
  const [selectedComponent, setSelectedComponent] =
    useState<FormComponent | null>(null);
  const [components, setComponents] = useState<FormComponent[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 初始化表单
  useEffect(() => {
    const editFormId = searchParams.get("formId");
    if (editFormId) {
      loadForm(editFormId);
    } else {
      // 创建新表单
      const newFormId = generateFormId();
      setFormId(newFormId);
    }
  }, [searchParams]);

  // 加载表单
  const loadForm = async (formIdToLoad: string) => {
    setLoading(true);
    try {
      const config = await formApi.loadForm(formIdToLoad);
      setFormId(config.formId);
      setFormTitle(config.title);
      setFormDescription(config.description || "");
      setFormLogo(config.logo || null);
      setComponents(config.components);
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("loadFailed"),
        description:
          error instanceof Error ? error.message : t("formLoadFailed"),
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!formId || !projectId) return;

    setSaving(true);
    try {
      const formConfig = serializeForm(
        formId,
        formTitle,
        formDescription,
        components,
        undefined, // 使用默认设置
        formLogo
      );

      await formApi.saveForm(projectId, formConfig);

      toast({
        title: t("saveSuccess"),
        description: t("formSavedSuccessfully"),
      });
      backToList();
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("saveFailed"),
        description:
          error instanceof Error ? error.message : t("formSaveFailed"),
      });
    } finally {
      setSaving(false);
    }
  };

  const handlePreview = () => {
    if (!formId) {
      toast({
        variant: "destructive",
        title: t("previewFailed"),
        description: t("pleaseFirstSaveForm"),
      });
      return;
    }

    // 先保存再预览
    handleSave().then(() => {
      navigate(`/form-preview/${formId}`);
    });
  };

  const handleAddComponent = (component: FormComponent) => {
    const newComponent = {
      ...component,
      id: `component_${Date.now()}`,
    };
    setComponents((prev) => [...prev, newComponent]);
  };

  const handleSelectComponent = (component: FormComponent | null) => {
    setSelectedComponent(component);
  };

  const handleUpdateComponent = (updatedComponent: FormComponent) => {
    setComponents((prev) =>
      prev.map((comp) =>
        comp.id === updatedComponent.id ? updatedComponent : comp
      )
    );
    setSelectedComponent(updatedComponent);
  };

  const handleDeleteComponent = (componentId: string) => {
    setComponents((prev) => prev.filter((comp) => comp.id !== componentId));
    if (selectedComponent?.id === componentId) {
      setSelectedComponent(null);
    }
  };

  const handleReorderComponents = (newComponents: FormComponent[]) => {
    setComponents(newComponents);
  };

  return (
    <div className="h-full flex flex-col">
      {/* 顶部工具栏 */}
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-6">
            {/* 表单基本信息 */}
            <div className="flex flex-col space-y-4">
              <div className="flex items-center space-x-2">
                <Label htmlFor="form-title" className="text-sm font-medium">
                  {t("formTitle")}:
                </Label>
                <Input
                  id="form-title"
                  value={formTitle}
                  onChange={(e) => setFormTitle(e.target.value)}
                  className="w-64"
                  placeholder={t("formTitlePlaceholder")}
                  disabled={loading}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Label
                  htmlFor="form-description"
                  className="text-sm font-medium"
                >
                  {t("formDescription")}:
                </Label>
                <Input
                  id="form-description"
                  value={formDescription}
                  onChange={(e) => setFormDescription(e.target.value)}
                  className="w-64"
                  placeholder={t("formDescriptionPlaceholder")}
                  disabled={loading}
                />
              </div>
            </div>

            {/* Logo 上传 */}
            <div className="pt-1">
              <LogoUpload
                value={formLogo}
                onChange={setFormLogo}
                disabled={loading || saving}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Undo className="w-4 h-4 mr-1" />
              {t("undo")}
            </Button>
            <Button variant="outline" size="sm">
              <Redo className="w-4 h-4 mr-1" />
              {t("redo")}
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreview}
              disabled={loading || saving}
            >
              <Eye className="w-4 h-4 mr-1" />
              {t("previewForm")}
            </Button>
            <Button size="sm" onClick={handleSave} disabled={loading || saving}>
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                  {t("savingForm")}
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-1" />
                  {t("saveForm")}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧组件库 */}
        <div className="w-80 border-r bg-gray-50 overflow-y-auto">
          <ComponentLibrary onAddComponent={handleAddComponent} />
        </div>

        {/* 中央设计画布 */}
        <div className="flex-1 bg-gray-100 overflow-auto">
          <DesignCanvas
            components={components}
            selectedComponent={selectedComponent}
            onSelectComponent={handleSelectComponent}
            onUpdateComponent={handleUpdateComponent}
            onDeleteComponent={handleDeleteComponent}
            onReorderComponents={handleReorderComponents}
            formTitle={formTitle}
            formDescription={formDescription}
            formLogo={formLogo}
          />
        </div>

        {/* 右侧属性面板 */}
        <div className="w-80 border-l bg-white overflow-y-auto">
          <PropertyPanel
            selectedComponent={selectedComponent}
            onUpdateComponent={handleUpdateComponent}
          />
        </div>
      </div>
    </div>
  );
};
