import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  Eye,
  Share2,
  Download,
  Settings,
  ExternalLink,
  Copy,
  Loader2,
} from "lucide-react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { useAuth } from "@/context/AuthContext";
import { useProject } from "@/context/ProjectContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { FormConfig } from "./types/form-types";
import { FormRenderer } from "./components/FormRenderer";
import { formApi } from "./services/form-api";

const FormPreview = () => {
  const { formId } = useParams<{ formId: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();
  const { currentProject } = useProject();
  const { toast } = useToast();
  const { t } = useTranslation();

  const [formConfig, setFormConfig] = useState<FormConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [shareUrl, setShareUrl] = useState<string>("");

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    if (formId) {
      loadFormConfig();
      generateShareUrl();
    }
  }, [formId]);

  const loadFormConfig = async () => {
    if (!formId) return;

    setLoading(true);
    setError(null);

    try {
      const config = await formApi.loadForm(formId);
      setFormConfig(config);
    } catch (err) {
      setError(err instanceof Error ? err.message : t("loadFormFailed"));
    } finally {
      setLoading(false);
    }
  };

  const generateShareUrl = () => {
    const baseUrl = window.location.origin;
    const url = `${baseUrl}/form/${formId}`;
    setShareUrl(url);
  };

  const handleSubmitForm = async (data: Record<string, any>) => {
    // TODO: 实现表单提交到后端
    console.log("Form submission:", data);

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    toast({
      title: t("submitSuccess"),
      description: t("submitSuccessMessage"),
    });
  };

  const handleCopyShareUrl = () => {
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: t("copyShareUrl"),
      description: t("shareUrlCopied"),
    });
  };

  const handleDownloadConfig = () => {
    if (!formConfig) return;

    const dataStr = JSON.stringify(formConfig, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${formConfig.title}_config.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handlebackToList = () => {
    navigate(-1);
  };

  const handleEditForm = () => {
    navigate(`/form-builder?formId=${formId}`);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="max-w-2xl mx-auto p-6">
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={handlebackToList} className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t("backToList")}
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (!formConfig) {
    return (
      <DashboardLayout>
        <div className="max-w-2xl mx-auto p-6">
          <Alert>
            <AlertDescription>{t("formConfigNotExist")}</AlertDescription>
          </Alert>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      {/* 头部操作栏 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={handlebackToList}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              {t("back")}
            </Button>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {formConfig.title}
            </h1>
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant="outline">{t("previewMode")}</Badge>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleEditForm}>
              <Settings className="w-4 h-4 mr-2" />
              {t("editForm")}
            </Button>
            <Button variant="outline" onClick={handleDownloadConfig}>
              <Download className="w-4 h-4 mr-2" />
              {t("exportConfig")}
            </Button>
            <Button onClick={handleCopyShareUrl}>
              <Share2 className="w-4 h-4 mr-2" />
              {t("shareForm")}
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：表单预览 */}
          <div className="lg:col-span-2">
            <FormRenderer
              config={formConfig}
              onSubmit={handleSubmitForm}
              className="shadow-lg"
            />
          </div>

          {/* 右侧：表单信息和设置 */}
          <div className="space-y-6">
            {/* 表单信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t("formInfo")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {t("formTitle")}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {formConfig.title}
                  </p>
                </div>

                {formConfig.description && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      {t("formDescription")}
                    </label>
                    <p className="text-sm text-gray-900 mt-1">
                      {formConfig.description}
                    </p>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {t("componentCount")}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {formConfig.components.length} {t("componentsUnit")}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {t("createdTime")}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {new Date(formConfig.createdAt).toLocaleString()}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {t("lastModified")}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {new Date(formConfig.updatedAt).toLocaleString()}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* 分享设置 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t("shareUrl")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {t("shareUrl")}
                  </label>
                  <div className="flex items-center space-x-2 mt-1">
                    <input
                      type="text"
                      value={shareUrl}
                      readOnly
                      className="w-[70%] flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md bg-gray-50"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCopyShareUrl}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => window.open(shareUrl, "_blank")}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  {t("openInNewWindow")}
                </Button>
              </CardContent>
            </Card>

            {/* 表单设置 */}
            {/* <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t("formSettings")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{t("allowMultipleSubmissions")}</span>
                  <Badge variant={formConfig.settings.allowMultipleSubmissions ? "default" : "secondary"}>
                    {formConfig.settings.allowMultipleSubmissions ? t("yes") : t("no")}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{t("requireLogin")}</span>
                  <Badge variant={formConfig.settings.requireLogin ? "default" : "secondary"}>
                    {formConfig.settings.requireLogin ? t("yes") : t("no")}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{t("collectEmail")}</span>
                  <Badge variant={formConfig.settings.collectEmail ? "default" : "secondary"}>
                    {formConfig.settings.collectEmail ? t("yes") : t("no")}
                  </Badge>
                </div>
              </CardContent>
            </Card> */}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default FormPreview;
