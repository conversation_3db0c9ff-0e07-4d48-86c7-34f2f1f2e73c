import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Loader2, AlertCircle, Home } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { FormConfig } from "./types/form-types";
import { FormRenderer } from "./components/FormRenderer";
import { formApi } from "./services/form-api";

const PublicForm = () => {
  const { formId } = useParams<{ formId: string }>();
  const { toast } = useToast();
  const { t } = useTranslation();

  const [formConfig, setFormConfig] = useState<FormConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (formId) {
      loadFormConfig();
    }
  }, [formId]);

  const loadFormConfig = async () => {
    if (!formId) return;

    setLoading(true);
    setError(null);

    try {
      const config = await formApi.loadForm(formId);
      setFormConfig(config);
    } catch (err) {
      setError(err instanceof Error ? err.message : t("loadFormFailed"));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitForm = async (data: Record<string, any>) => {
    try {
      // TODO: 实现表单提交到后端API
      console.log("Public form submission:", data);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 可以在这里添加数据验证、处理等逻辑
      
      toast({
        title: t("submitSuccess"),
        description: t("submitSuccessMessage"),
      });
    } catch (error) {
      console.error("Form submission error:", error);
      throw new Error(t("submitFailed"));
    }
  };

  const handleGoHome = () => {
    window.location.href = "/";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">{t("loadingForm")}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto p-6">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={handleGoHome} className="w-full">
            <Home className="w-4 h-4 mr-2" />
            {t("goHome")}
          </Button>
        </div>
      </div>
    );
  }

  if (!formConfig) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto p-6">
          <Alert className="mb-4">
            <AlertDescription>{t("formConfigNotExist")}</AlertDescription>
          </Alert>
          <Button onClick={handleGoHome} className="w-full">
            <Home className="w-4 h-4 mr-2" />
            {t("goHome")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* 页面头部 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-4">
            {t("onlineForm")}
          </div>
        </div>

        {/* 表单渲染 */}
        <FormRenderer
          config={formConfig}
          onSubmit={handleSubmitForm}
          className="shadow-lg border-0"
        />

        {/* 页面底部 */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>
            {t("poweredBy")}{" "}
            <a
              href="/"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              {t("formBuilderName")}
            </a>{" "}
            {t("techSupport")}
          </p>
        </div>
      </div>
    </div>
  );
};

export default PublicForm;
