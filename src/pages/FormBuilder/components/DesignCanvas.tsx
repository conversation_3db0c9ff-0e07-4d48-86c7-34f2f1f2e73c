import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "@/hooks/useTranslation";
import {
  Trash2,
  <PERSON><PERSON>,
  Settings,
  GripVertical,
  Plus
} from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { FormComponent } from "../types/form-types";
import { ComponentRenderer } from "./ComponentRenderer";

interface DesignCanvasProps {
  components: FormComponent[];
  selectedComponent: FormComponent | null;
  onSelectComponent: (component: FormComponent | null) => void;
  onUpdateComponent: (component: FormComponent) => void;
  onDeleteComponent: (componentId: string) => void;
  onReorderComponents: (components: FormComponent[]) => void;
  formTitle?: string;
  formDescription?: string;
  formLogo?: string | null;
}

interface SortableItemProps {
  component: FormComponent;
  isSelected: boolean;
  onSelect: (component: FormComponent) => void;
  onDuplicate: (component: FormComponent) => void;
  onDelete: (componentId: string) => void;
}

const SortableItem = ({ component, isSelected, onSelect, onDuplicate, onDelete }: SortableItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: component.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleComponentClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    onSelect(component);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`relative group border-2 rounded-lg p-4 transition-all ${
        isSelected
          ? 'border-blue-500 bg-blue-50'
          : 'border-transparent hover:border-gray-300'
      }`}
      onClick={handleComponentClick}
    >
      {/* 拖拽手柄 */}
      <div
        className="absolute left-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity cursor-move"
        {...attributes}
        {...listeners}
      >
        <GripVertical className="w-4 h-4 text-gray-400" />
      </div>

      {/* 组件类型标签 */}
      {isSelected && (
        <div className="absolute -top-2 left-4">
          <Badge variant="secondary" className="text-xs">
            {component.type}
          </Badge>
        </div>
      )}

      {/* 操作按钮 */}
      {isSelected && (
        <div className="absolute -top-2 right-4 flex space-x-1">
          <Button
            size="sm"
            variant="outline"
            className="h-6 w-6 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onDuplicate(component);
            }}
          >
            <Copy className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(component.id);
            }}
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      )}

      {/* 渲染组件 */}
      <ComponentRenderer
        component={component}
        isPreview={false}
      />
    </div>
  );
};

export const DesignCanvas = ({
  components,
  selectedComponent,
  onSelectComponent,
  onUpdateComponent,
  onDeleteComponent,
  onReorderComponents,
  formTitle,
  formDescription,
  formLogo,
}: DesignCanvasProps) => {
  const { t } = useTranslation();
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleCanvasClick = () => {
    onSelectComponent(null);
  };

  const handleDuplicateComponent = (component: FormComponent) => {
    const duplicatedComponent: FormComponent = {
      ...component,
      id: `${component.type}_${Date.now()}`,
      label: `${component.label} (${t('copyLabel')})`,
    };
    onUpdateComponent(duplicatedComponent);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = components.findIndex(item => item.id === active.id);
      const newIndex = components.findIndex(item => item.id === over?.id);

      const newComponents = arrayMove(components, oldIndex, newIndex);
      onReorderComponents(newComponents);
    }
  };

  return (
    <div className="h-full p-6" onClick={handleCanvasClick}>
      <div className="max-w-2xl mx-auto">
        <Card className="min-h-[600px] bg-white shadow-sm">
          <CardContent className="p-6">
            {/* 表单标题区域 */}
            <div className="mb-8 text-center">
              {/* Logo */}
              {formLogo && (
                <div className="mb-4">
                  <img
                    src={formLogo}
                    alt="Form Logo"
                    className="w-16 h-16 mx-auto object-cover rounded-lg"
                  />
                </div>
              )}

              {/* 标题 */}
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {formTitle || t('formTitleDefault')}
              </h1>

              {/* 描述 */}
              <p className="text-gray-600">
                {formDescription || t('fillFollowingInfo')}
              </p>
            </div>

            {/* 组件列表 */}
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <div className="space-y-4">
                {components.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Plus className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {t('startDesigning')}
                    </h3>
                    <p className="text-gray-600">
                      {t('dragComponentsHere')}
                    </p>
                  </div>
                ) : (
                  <SortableContext
                    items={components.map(c => c.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {components.map((component) => (
                      <SortableItem
                        key={component.id}
                        component={component}
                        isSelected={selectedComponent?.id === component.id}
                        onSelect={onSelectComponent}
                        onDuplicate={handleDuplicateComponent}
                        onDelete={onDeleteComponent}
                      />
                    ))}
                  </SortableContext>
                )}
              </div>
            </DndContext>

            {/* 提交按钮区域 */}
            {components.length > 0 && (
              <div className="mt-8 pt-6 border-t">
                <Button className="w-full" size="lg">
                  {t('submit')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
