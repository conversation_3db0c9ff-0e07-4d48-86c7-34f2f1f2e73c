import { useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { FormConfig, FormComponent } from "../types/form-types";
import { ComponentRenderer } from "./ComponentRenderer";

interface FormRendererProps {
  config: FormConfig;
  onSubmit?: (data: Record<string, any>) => Promise<void>;
  className?: string;
  showTitle?: boolean;
  showDescription?: boolean;
}

interface FormState {
  isSubmitting: boolean;
  isSubmitted: boolean;
  error: string | null;
}

export const FormRenderer = ({
  config,
  onSubmit,
  className = "",
  showTitle = true,
  showDescription = true,
}: FormRendererProps) => {
  const [formState, setFormState] = useState<FormState>({
    isSubmitting: false,
    isSubmitted: false,
    error: null,
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm();

  const formValues = watch();

  // 处理表单提交
  const handleFormSubmit = useCallback(
    async (data: Record<string, any>) => {
      if (!onSubmit) return;

      setFormState({
        isSubmitting: true,
        isSubmitted: false,
        error: null,
      });

      try {
        await onSubmit(data);
        setFormState({
          isSubmitting: false,
          isSubmitted: true,
          error: null,
        });

        // 如果不允许多次提交，保持提交状态
        if (!config.settings.allowMultipleSubmissions) {
          return;
        }

        // 重置表单
        setTimeout(() => {
          reset();
          setFormState({
            isSubmitting: false,
            isSubmitted: false,
            error: null,
          });
        }, 3000);
      } catch (error) {
        setFormState({
          isSubmitting: false,
          isSubmitted: false,
          error: error instanceof Error ? error.message : config.settings.errorMessage,
        });
      }
    },
    [onSubmit, config.settings, reset]
  );

  // 处理组件值变化
  const handleComponentChange = useCallback(
    (componentId: string, value: any) => {
      setValue(componentId, value);
    },
    [setValue]
  );

  // 获取组件值
  const getComponentValue = useCallback(
    (componentId: string) => {
      return formValues[componentId];
    },
    [formValues]
  );

  // 验证组件
  const getComponentValidation = (component: FormComponent) => {
    const rules: any = {};

    if (component.required) {
      rules.required = `${component.label}是必填项`;
    }

    // 根据组件类型添加特定验证
    switch (component.type) {
      case 'email-input':
        rules.pattern = {
          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
          message: '请输入有效的邮箱地址',
        };
        break;
      case 'phone-input':
        rules.pattern = {
          value: /^1[3-9]\d{9}$/,
          message: '请输入有效的手机号码',
        };
        break;
      case 'id-number':
        rules.pattern = {
          value: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
          message: '请输入有效的身份证号码',
        };
        break;
    }

    return rules;
  };

  // 如果已提交且不允许多次提交，显示成功消息
  if (formState.isSubmitted && !config.settings.allowMultipleSubmissions) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          {/* Logo */}
          {config.logo && (
            <div className="mb-4">
              <img
                src={config.logo}
                alt="Form Logo"
                className="w-16 h-16 mx-auto object-cover rounded-lg"
              />
            </div>
          )}

          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            提交成功！
          </h3>
          <p className="text-gray-600">{config.settings.successMessage}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {(showTitle || showDescription || config.logo) && (
        <CardHeader className="text-center">
          {/* Logo */}
          {config.logo && (
            <div className="mb-4">
              <img
                src={config.logo}
                alt="Form Logo"
                className="w-16 h-16 mx-auto object-cover rounded-lg"
              />
            </div>
          )}

          {/* 标题 */}
          {showTitle && (
            <CardTitle className="text-2xl font-bold text-gray-900">
              {config.title}
            </CardTitle>
          )}

          {/* 描述 */}
          {showDescription && config.description && (
            <p className="text-gray-600 mt-2">{config.description}</p>
          )}
        </CardHeader>
      )}

      <CardContent className="p-6">
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* 渲染表单组件 */}
          {config.components.map((component) => (
            <div key={component.id}>
              <ComponentRenderer
                component={component}
                isPreview={true}
                value={getComponentValue(component.id)}
                onChange={(value) => handleComponentChange(component.id, value)}
              />
              
              {/* 显示验证错误 */}
              {errors[component.id] && (
                <p className="text-red-500 text-sm mt-1">
                  {errors[component.id]?.message as string}
                </p>
              )}
              
              {/* 注册表单字段 */}
              <input
                type="hidden"
                {...register(component.id, getComponentValidation(component))}
              />
            </div>
          ))}

          {/* 错误消息 */}
          {formState.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{formState.error}</AlertDescription>
            </Alert>
          )}

          {/* 成功消息 */}
          {formState.isSubmitted && config.settings.allowMultipleSubmissions && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                {config.settings.successMessage}
              </AlertDescription>
            </Alert>
          )}

          {/* 提交按钮 */}
          <div className="pt-4">
            <Button
              type="submit"
              className="w-full"
              size="lg"
              disabled={formState.isSubmitting}
            >
              {formState.isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  提交中...
                </>
              ) : (
                config.settings.submitText
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

// 简化的表单渲染器，用于预览
export const FormPreview = ({ config }: { config: FormConfig }) => {
  return (
    <FormRenderer
      config={config}
      onSubmit={async (data) => {
        console.log("Preview form submission:", data);
        // 模拟提交延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
      }}
    />
  );
};
