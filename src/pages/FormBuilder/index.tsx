import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { useAuth } from "@/context/AuthContext";
import { useProject } from "@/context/ProjectContext";
import { useTranslation } from "@/hooks/useTranslation";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { FormList } from "./FormList";
import { FormDesigner } from "./FormDesigner";

const FormBuilder = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();

  const [activeTab, setActiveTab] = useState<"list" | "designer">("list");

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  // 检查是否有formId参数，如果有则直接进入设计器
  useEffect(() => {
    const formId = searchParams.get("formId");
    if (formId) {
      setActiveTab("designer");
    }
  }, [searchParams]);

  // 如果项目ID不存在，不渲染内容
  if (!currentProject?.id) {
    return <DashboardLayout>{t("loading")}</DashboardLayout>;
  }

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {activeTab === "list" && (
          <FormList
            projectId={currentProject.id}
            toDesigner={() => {
              console.log('to designer');
              
              setActiveTab("designer");
            }}
          />
        )}
        {activeTab === "designer" && (
          <FormDesigner
            projectId={currentProject.id}
            backToList={() => setActiveTab("list")}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default FormBuilder;
