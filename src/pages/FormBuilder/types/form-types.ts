// 表单组件基础类型
export interface FormComponent {
  id: string;
  type: ComponentType;
  label: string;
  placeholder?: string;
  required?: boolean;
  validation?: ValidationRule[];
  options?: ComponentOption[];
  defaultValue?: any;
  position?: Position;
  size?: Size;
  style?: ComponentStyle;
  properties?: Record<string, any>;
}

// 组件类型枚举
export type ComponentType =
  // 基础组件
  | "text-input"
  | "textarea"
  | "select"
  | "radio"
  | "checkbox"
  | "date-picker"
  | "number-input"
  | "email-input"
  | "phone-input"
  | "file-upload"
  | "heading"
  | "paragraph"
  | "divider"
  | "button"
  // 复用组件
  | "full-name"
  | "contact-info"
  | "address"
  | "id-number"
  | "bank-card";

// 组件选项
export interface ComponentOption {
  label: string;
  value: string | number;
}

// 验证规则
export interface ValidationRule {
  type: "required" | "minLength" | "maxLength" | "pattern" | "email" | "phone";
  value?: any;
  message?: string;
}

// 位置信息
export interface Position {
  x: number;
  y: number;
}

// 尺寸信息
export interface Size {
  width: string | number;
  height: string | number;
}

// 组件样式
export interface ComponentStyle {
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  borderRadius?: number;
  fontSize?: number;
  fontWeight?: string;
  padding?: string;
  margin?: string;
}

// 表单配置
export interface FormConfig {
  formId: string;
  title: string;
  description?: string;
  logo?: string; // base64 encoded image
  url: string;
  settings: FormSettings;
  components: FormComponent[];
  createdAt: string;
  updatedAt: string;
}

// 后端表单响应类型（对应 FormResponse）
export interface FormResponse {
  id: string;
  projectId: string;
  name: string;
  description?: string;
  logo?: string;
  content: string; // JSON 字符串格式的表单配置
  url?: string;
  active: boolean;
  createTime: string;
  updateTime: string;
}

// 后端表单模板响应类型（对应 TemplateResponse）
export interface TemplateResponse {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  content: string; // JSON 字符串格式的表单配置
  createTime: string;
}

// 添加表单请求类型（对应 AddFormRequest）
export interface AddFormRequest {
  projectId: string;
  name: string;
  description?: string;
  logo?: string;
  content: string; // JSON 字符串格式的表单配置
  url?: string;
  active: boolean;
}

// 更新表单请求类型（对应 UpdateFormRequest）
export interface UpdateFormRequest {
  name?: string;
  description?: string;
  logo?: string;
  content?: string; // JSON 字符串格式的表单配置
  active?: boolean;
}

// 表单设置
export interface FormSettings {
  submitText: string;
  successMessage: string;
  errorMessage: string;
  allowMultipleSubmissions: boolean;
  requireLogin: boolean;
  collectEmail: boolean;
  notificationEmail?: string;
  redirectUrl?: string;
}

// 组件库分类
export interface ComponentCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  components: ComponentTemplate[];
}

// 组件模板
export interface ComponentTemplate {
  type: ComponentType;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  defaultProps: Partial<FormComponent>;
  description?: string;
}

// 拖拽相关类型
export interface DragItem {
  type: "component";
  componentType: ComponentType;
  isNew: boolean;
  component?: FormComponent;
}

// 表单提交数据
export interface FormSubmission {
  id: string;
  formId: string;
  data: Record<string, any>;
  submittedAt: string;
  userAgent?: string;
  ipAddress?: string;
}
