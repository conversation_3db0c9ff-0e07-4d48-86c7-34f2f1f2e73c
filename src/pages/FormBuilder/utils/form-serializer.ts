import { FormComponent, FormConfig, FormSettings } from "../types/form-types";

// 默认表单设置
export const defaultFormSettings: FormSettings = {
  submitText: "提交",
  successMessage: "表单提交成功！",
  errorMessage: "提交失败，请重试。",
  allowMultipleSubmissions: true,
  requireLogin: false,
  collectEmail: false,
};

// 序列化表单配置为JSON
export const serializeForm = (
  formId: string,
  title: string,
  description: string,
  components: FormComponent[],
  settings: FormSettings = defaultFormSettings,
  logo?: string | null
): FormConfig => {
  const now = new Date().toISOString();

  return {
    formId,
    title,
    description,
    logo: logo || undefined,
    settings,
    components: components.map((component, index) => ({
      ...component,
      position: { x: 0, y: index }, // 确保位置信息正确
    })),
    url:'',
    createdAt: now,
    updatedAt: now,
  };
};

// 从JSON反序列化表单配置
export const deserializeForm = (json: string): FormConfig => {
  try {
    const config: FormConfig = JSON.parse(json);
    
    // 验证必要字段
    if (!config.formId || !config.title || !Array.isArray(config.components)) {
      throw new Error("Invalid form configuration");
    }
    
    // 确保设置有默认值
    config.settings = {
      ...defaultFormSettings,
      ...config.settings,
    };
    
    // 确保组件有必要的字段
    config.components = config.components.map((component, index) => ({
      ...component,
      id: component.id || `component_${index}_${Date.now()}`,
      position: component.position || { x: 0, y: index },
    }));
    
    return config;
  } catch (error) {
    throw new Error(`Failed to parse form configuration: ${error}`);
  }
};

// 导出表单配置为JSON字符串
export const exportFormConfig = (config: FormConfig): string => {
  return JSON.stringify(config, null, 2);
};

// 验证表单配置
export const validateFormConfig = (config: FormConfig): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // 验证基本字段
  if (!config.formId) {
    errors.push("Form ID is required");
  }
  
  if (!config.title || config.title.trim() === "") {
    errors.push("Form title is required");
  }
  
  if (!Array.isArray(config.components)) {
    errors.push("Components must be an array");
  } else {
    // 验证组件
    config.components.forEach((component, index) => {
      if (!component.id) {
        errors.push(`Component at index ${index} is missing ID`);
      }
      
      if (!component.type) {
        errors.push(`Component at index ${index} is missing type`);
      }
      
      if (!component.label && !['divider'].includes(component.type)) {
        errors.push(`Component at index ${index} is missing label`);
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// 生成唯一的表单ID
export const generateFormId = (): string => {
  return `form_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 生成组件ID
export const generateComponentId = (type: string): string => {
  return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
};

// 克隆组件（用于复制功能）
export const cloneComponent = (component: FormComponent): FormComponent => {
  return {
    ...component,
    id: generateComponentId(component.type),
    label: `${component.label} (副本)`,
  };
};

// 创建空白表单配置
export const createBlankForm = (title: string = "新建表单"): FormConfig => {
  return serializeForm(
    generateFormId(),
    title,
    "请填写以下信息",
    [],
    defaultFormSettings
  );
};

// 表单配置模板
export const formTemplates = {
  // 联系表单模板
  contact: (): FormConfig => {
    const components: FormComponent[] = [
      {
        id: generateComponentId("full-name"),
        type: "full-name",
        label: "姓名",
        required: true,
      },
      {
        id: generateComponentId("email-input"),
        type: "email-input",
        label: "邮箱地址",
        placeholder: "请输入您的邮箱地址",
        required: true,
      },
      {
        id: generateComponentId("phone-input"),
        type: "phone-input",
        label: "电话号码",
        placeholder: "请输入您的电话号码",
        required: false,
      },
      {
        id: generateComponentId("textarea"),
        type: "textarea",
        label: "留言内容",
        placeholder: "请输入您的留言...",
        required: true,
      },
    ];
    
    return serializeForm(
      generateFormId(),
      "联系我们",
      "请填写您的联系信息，我们会尽快回复您。",
      components
    );
  },
  
  // 注册表单模板
  registration: (): FormConfig => {
    const components: FormComponent[] = [
      {
        id: generateComponentId("full-name"),
        type: "full-name",
        label: "姓名",
        required: true,
      },
      {
        id: generateComponentId("email-input"),
        type: "email-input",
        label: "邮箱地址",
        placeholder: "请输入邮箱地址",
        required: true,
      },
      {
        id: generateComponentId("phone-input"),
        type: "phone-input",
        label: "手机号码",
        placeholder: "请输入手机号码",
        required: true,
      },
      {
        id: generateComponentId("date-picker"),
        type: "date-picker",
        label: "出生日期",
        placeholder: "请选择出生日期",
        required: false,
      },
      {
        id: generateComponentId("radio"),
        type: "radio",
        label: "性别",
        required: false,
        options: [
          { label: "男", value: "male" },
          { label: "女", value: "female" },
          { label: "其他", value: "other" },
        ],
      },
      {
        id: generateComponentId("address"),
        type: "address",
        label: "地址",
        required: false,
      },
    ];
    
    return serializeForm(
      generateFormId(),
      "用户注册",
      "请填写以下信息完成注册。",
      components
    );
  },
  
  // 反馈表单模板
  feedback: (): FormConfig => {
    const components: FormComponent[] = [
      {
        id: generateComponentId("text-input"),
        type: "text-input",
        label: "姓名",
        placeholder: "请输入您的姓名",
        required: true,
      },
      {
        id: generateComponentId("email-input"),
        type: "email-input",
        label: "邮箱地址",
        placeholder: "请输入邮箱地址",
        required: true,
      },
      {
        id: generateComponentId("select"),
        type: "select",
        label: "反馈类型",
        placeholder: "请选择反馈类型",
        required: true,
        options: [
          { label: "产品建议", value: "suggestion" },
          { label: "问题报告", value: "bug" },
          { label: "功能请求", value: "feature" },
          { label: "其他", value: "other" },
        ],
      },
      {
        id: generateComponentId("radio"),
        type: "radio",
        label: "满意度评分",
        required: true,
        options: [
          { label: "非常满意", value: "5" },
          { label: "满意", value: "4" },
          { label: "一般", value: "3" },
          { label: "不满意", value: "2" },
          { label: "非常不满意", value: "1" },
        ],
      },
      {
        id: generateComponentId("textarea"),
        type: "textarea",
        label: "详细反馈",
        placeholder: "请详细描述您的反馈...",
        required: true,
      },
    ];
    
    return serializeForm(
      generateFormId(),
      "用户反馈",
      "您的反馈对我们非常重要，请填写以下信息。",
      components
    );
  },
};
