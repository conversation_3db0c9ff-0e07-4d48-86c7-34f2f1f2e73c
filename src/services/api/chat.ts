import store from "store2";
import { formatToUUID } from "@/utils/common.ts";

// 聊天接口
interface ChatRequest {
  projectId: string;
  question: string;
  sessionId?: string;
}

interface ChatResponse {
  answer: string;
  reference: {
    chunks: any[];
  };
  audio_binary: string | null;
  id: string;
  session_id: string;
}

// 聊天记录相关接口
export type SessionStatus = "Open" | "Closed";
export type ChannelType = "WebChat" | "WhatsApp" | "Telegram";

export interface SessionDetails {
  status: SessionStatus;
  channel: ChannelType;
}

export interface CustomerInfo {
  country: string;
  city: string;
  browser: string;
  system: string;
}

export interface SessionResponse {
  id: string;
  projectId: string;
  name: string;
  details: SessionDetails;
  customerInfo: CustomerInfo;
  createTime: string;
}

export interface ChatMessage {
  sessionId: string;
  question: string;
  answer: string;
  reference: any;
  like: number; // 0: unlike, 1: like
  createTime: string;
}

interface GetSessionsRequest {
  projectId: string;
  pageNumber?: number;
  pageSize?: number;
}

interface GetChatsRequest {
  sessionId: string;
  pageNumber?: number;
  pageSize?: number;
}

export const sendChatMessage = async (
  data: ChatRequest
): Promise<ReadableStream<Uint8Array> | null> => {
  console.log("data.sessionId:", data.sessionId);
  if (data.sessionId) {
    data.sessionId = formatToUUID(data.sessionId);
  }

  const response = await fetch("/v1/chat", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.body;
};

export const getSessions = async (
  data: GetSessionsRequest
): Promise<SessionResponse[]> => {
  const params = new URLSearchParams({
    projectId: data.projectId,
    ...(data.pageNumber && { pageNumber: data.pageNumber.toString() }),
    ...(data.pageSize && { pageSize: data.pageSize.toString() }),
  });

  const response = await fetch(`/v1/sessions?${params}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

export const getChats = async (
  data: GetChatsRequest
): Promise<ChatMessage[]> => {
  const params = new URLSearchParams({
    sessionId: data.sessionId,
    ...(data.pageNumber && { pageNumber: data.pageNumber.toString() }),
    ...(data.pageSize && { pageSize: data.pageSize.toString() }),
  });

  const response = await fetch(`/v1/chats?${params}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 导出类型
export type {
  ChatRequest,
  ChatResponse,
  GetSessionsRequest,
  GetChatsRequest,
};
