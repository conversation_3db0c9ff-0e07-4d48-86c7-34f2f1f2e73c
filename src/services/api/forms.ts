import store from "store2";

// 表单相关的 API 接口类型定义
export interface FormResponse {
  id: string;
  projectId: string;
  name: string;
  description?: string;
  logo?: string;
  content: string; // JSON 字符串格式的表单配置
  url?: string;
  active: boolean;
  createTime: string;
  updateTime: string;
}

export interface TemplateResponse {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  content: string; // JSON 字符串格式的表单配置
  createTime: string;
}

export interface AddFormRequest {
  projectId: string;
  name: string;
  description?: string;
  logo?: string;
  content: string; // JSON 字符串格式的表单配置
  url?: string;
  active: boolean;
}

export interface UpdateFormRequest {
  name?: string;
  description?: string;
  logo?: string;
  content?: string; // JSON 字符串格式的表单配置
  active?: boolean;
}

// 获取所有表单
export const getForms = async (): Promise<FormResponse[]> => {
  const response = await fetch("/v1/forms", {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 获取表单模板
export const getFormTemplates = async (): Promise<TemplateResponse[]> => {
  const response = await fetch("/v1/formTemplates", {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 创建表单
export const createForm = async (data: AddFormRequest): Promise<FormResponse> => {
  const response = await fetch("/v1/forms", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 更新表单
export const updateForm = async (
  formId: string,
  data: UpdateFormRequest
): Promise<FormResponse> => {
  const response = await fetch(`/v1/forms/${formId}`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 删除表单
export const deleteForm = async (formId: string): Promise<void> => {
  const response = await fetch(`/v1/forms/${formId}`, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }
};

// 导出类型
export type {
  FormResponse,
  TemplateResponse,
  AddFormRequest,
  UpdateFormRequest,
};
