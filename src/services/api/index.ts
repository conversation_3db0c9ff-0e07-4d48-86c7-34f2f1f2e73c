// 统一导出所有API接口和类型

// 认证相关
export {
  userLogin,
  userRegister,
  getUserProfile,
  updateUserProfile,
  deleteUserAccount,
} from "./auth";
export type { LoginRequest, RegisterRequest, UserProfile } from "./auth";

// 项目管理相关
export {
  createProject,
  updateProject,
  getProjects,
  getProject,
} from "./projects";
export type {
  ProjectSettings,
  CreateProjectRequest,
  CreateProjectResponse,
  UpdateProjectRequest,
  Project,
} from "./projects";

// 知识库相关
export {
  uploadFile,
  uploadWebsite,
  getKnowledges,
  getKnowledgesStatistics,
} from "./knowledge";
export type {
  KnowledgeType,
  FileParseStatus,
  KnowledgeResponse,
  FileTypeStatistics,
  UploadFileRequest,
  UploadWebsiteRequest,
  GetKnowledgesRequest,
  KnowledgeListResponse,
} from "./knowledge";

// 聊天相关
export {
  sendChatMessage,
  getSessions,
  getChats,
} from "./chat";
export type {
  SessionStatus,
  ChannelType,
  SessionDetails,
  CustomerInfo,
  SessionResponse,
  ChatMessage,
  ChatRequest,
  ChatResponse,
  GetSessionsRequest,
  GetChatsRequest,
} from "./chat";

// 统计分析相关
export {
  statisticsDashboard,
  statisticsChat,
} from "./statistics";
export type {
  DashboardStatistics,
  ChatStatistics,
} from "./statistics";

// 计费套餐相关
export {
  getCurrentPlan,
  getAvailablePlans,
  upgradePlan,
  getCurrentProduct,
} from "./billing";
export type {
  IntervalType,
  ProductResponse,
  PlanLimits,
  BotLimits,
  CurrentProductResponse,
  CurrentPlan,
  AvailablePlan,
  UpgradePlanRequest,
} from "./billing";

// 表单相关
export {
  getForms,
  getFormTemplates,
  createForm,
  updateForm,
  deleteForm,
} from "./forms";
export type {
  FormResponse,
  TemplateResponse,
  AddFormRequest,
  UpdateFormRequest,
} from "./forms";

// FAQ相关
export {
  getFaqs,
  createFaq,
  updateFaq,
  deleteFaq,
} from "./faq";
export type {
  AddFaqRequest,
  UpdateFaqRequest,
  GetFaqsQuery,
  FaqResponse,
  Page,
} from "./faq";
